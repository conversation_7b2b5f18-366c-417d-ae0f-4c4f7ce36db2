#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接清理原始文件，去除所有中文注释和反混淆总结
"""

import re
import shutil

def clean_original_file():
    """直接清理原始文件"""

    input_file = "521-remove_monitor-read/521-remove_monitor-beautify-un.js"
    backup_file = "521-remove_monitor-read/521-remove_monitor-beautify-un-backup.js"

    print(f"正在处理文件: {input_file}")

    # 先备份原文件
    shutil.copy2(input_file, backup_file)
    print(f"已备份原文件到: {backup_file}")

    # 读取文件
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    print(f"原始文件行数: {len(lines)}")

    # 过滤掉包含中文字符的注释行
    filtered_lines = []
    removed_count = 0

    for i, line in enumerate(lines, 1):
        # 检查是否是包含中文字符的注释行
        if re.match(r'^\s*//.*[\u4e00-\u9fff]', line):
            removed_count += 1
            continue

        # 检查是否是包含中文字符的多行注释开始
        if re.match(r'^\s*/\*.*[\u4e00-\u9fff]', line):
            removed_count += 1
            continue

        # 检查是否是包含中文字符的多行注释中间或结束
        if re.match(r'^\s*\*.*[\u4e00-\u9fff]', line):
            removed_count += 1
            continue

        # 检查是否是多行注释结束且包含中文
        if re.match(r'^\s*\*/.*[\u4e00-\u9fff]', line):
            removed_count += 1
            continue

        # 保留其他行
        filtered_lines.append(line)

    print(f"删除的中文注释行数: {removed_count}")
    print(f"剩余行数: {len(filtered_lines)}")

    # 写回原文件
    with open(input_file, 'w', encoding='utf-8') as f:
        f.writelines(filtered_lines)

    print(f"原文件已清理完成")

    # 修复已知的正则表达式错误和重复类定义
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 修复Unicode范围错误
    if '\\u0A93-\\uAA8' in content:
        content = content.replace('\\u0A93-\\uAA8', '\\u0A93-\\u0AA8')
        print("已修复Unicode正则表达式错误")

    # 删除重复的AugmentExtension类定义，只保留最后一个
    class_pattern = r'class AugmentExtension extends [^{]*\{'
    matches = list(re.finditer(class_pattern, content))

    if len(matches) > 1:
        print(f"找到 {len(matches)} 个重复的AugmentExtension类定义，正在删除前 {len(matches)-1} 个")

        # 找到每个类定义的范围并删除前面的
        class_ranges = []
        for i, match in enumerate(matches):
            start = match.start()

            # 向前找到注释开始
            comment_start = start
            lines_before = content[:start].split('\n')
            for j in range(len(lines_before) - 1, -1, -1):
                line = lines_before[j].strip()
                if line.startswith('/**') or line.startswith('//') or line == '':
                    comment_start = sum(len(l) + 1 for l in lines_before[:j])
                else:
                    break

            # 找到类定义的结束位置
            brace_count = 0
            pos = match.end() - 1
            while pos < len(content):
                if content[pos] == '{':
                    brace_count += 1
                elif content[pos] == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end = pos + 1
                        break
                pos += 1
            else:
                if i + 1 < len(matches):
                    end = matches[i + 1].start()
                else:
                    end = len(content)

            class_ranges.append((comment_start, end))

        # 删除前面的重复定义
        offset = 0
        for i in range(len(class_ranges) - 1):
            start, end = class_ranges[i]
            start -= offset
            end -= offset
            content = content[:start] + content[end:]
            offset += (end - start)

        print("已删除重复的类定义")

    # 写回文件
    with open(input_file, 'w', encoding='utf-8') as f:
        f.write(content)

    return input_file

if __name__ == "__main__":
    clean_original_file()
