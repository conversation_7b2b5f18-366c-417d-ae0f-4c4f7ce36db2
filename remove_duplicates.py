#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除重复的类定义，只保留最后一个
"""

import re

def remove_duplicate_classes(file_path):
    """删除重复的AugmentExtension类定义，只保留最后一个"""
    
    print(f"正在处理文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"原始文件大小: {len(content)} 字符")
    
    # 找到所有 AugmentExtension 类定义的位置
    class_pattern = r'class AugmentExtension extends [^{]*\{'
    matches = list(re.finditer(class_pattern, content))
    
    print(f"找到 {len(matches)} 个 AugmentExtension 类定义")
    
    if len(matches) <= 1:
        print("没有重复的类定义")
        return file_path
    
    # 找到每个类定义的结束位置
    class_ranges = []
    for i, match in enumerate(matches):
        start = match.start()
        
        # 从类定义开始位置向前找到注释开始
        comment_start = start
        lines_before = content[:start].split('\n')
        for j in range(len(lines_before) - 1, -1, -1):
            line = lines_before[j].strip()
            if line.startswith('/**') or line.startswith('//') or line == '':
                comment_start = sum(len(l) + 1 for l in lines_before[:j])
            else:
                break
        
        # 找到类定义的结束位置（匹配大括号）
        brace_count = 0
        pos = match.end() - 1  # 从第一个 { 开始
        while pos < len(content):
            if content[pos] == '{':
                brace_count += 1
            elif content[pos] == '}':
                brace_count -= 1
                if brace_count == 0:
                    end = pos + 1
                    break
            pos += 1
        else:
            # 如果没找到匹配的结束括号，使用下一个类定义的开始位置
            if i + 1 < len(matches):
                end = matches[i + 1].start()
            else:
                end = len(content)
        
        class_ranges.append((comment_start, end))
        print(f"类定义 {i+1}: 位置 {comment_start}-{end}")
    
    # 删除前面的重复定义，只保留最后一个
    new_content = content
    offset = 0
    
    for i in range(len(class_ranges) - 1):  # 保留最后一个
        start, end = class_ranges[i]
        start -= offset
        end -= offset
        
        print(f"删除类定义 {i+1}: 位置 {start}-{end}")
        new_content = new_content[:start] + new_content[end:]
        offset += (end - start)
    
    # 写回文件
    output_path = file_path.replace('.js', '-dedup.js')
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"去重后的文件已保存为: {output_path}")
    print(f"新文件大小: {len(new_content)} 字符")
    return output_path

if __name__ == "__main__":
    remove_duplicate_classes("521-remove_monitor-read/521-remove_monitor-beautify-final.js")
