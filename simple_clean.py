#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单清理：只删除中文注释，保持代码结构完整
"""

import re

def simple_clean(file_path):
    """只删除中文注释，不处理重复代码"""
    
    print(f"正在处理文件: {file_path}")
    
    # 读取原始文件
    with open("521-remove_monitor-read/521-remove_monitor-beautify-un.js", 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print(f"原始文件行数: {len(lines)}")
    
    # 过滤掉包含中文字符的注释行
    filtered_lines = []
    removed_count = 0
    
    for i, line in enumerate(lines, 1):
        # 检查是否是包含中文字符的注释行
        if re.match(r'^\s*//.*[\u4e00-\u9fff]', line):
            removed_count += 1
            continue
        
        # 检查是否是包含中文字符的多行注释开始
        if re.match(r'^\s*/\*.*[\u4e00-\u9fff]', line):
            removed_count += 1
            continue
            
        # 检查是否是包含中文字符的多行注释中间或结束
        if re.match(r'^\s*\*.*[\u4e00-\u9fff]', line):
            removed_count += 1
            continue
            
        # 检查是否是多行注释结束且包含中文
        if re.match(r'^\s*\*/.*[\u4e00-\u9fff]', line):
            removed_count += 1
            continue
        
        # 保留其他行
        filtered_lines.append(line)
    
    print(f"删除的中文注释行数: {removed_count}")
    print(f"剩余行数: {len(filtered_lines)}")
    
    # 写回文件
    output_path = "521-remove_monitor-read/521-remove_monitor-beautify-final.js"
    with open(output_path, 'w', encoding='utf-8') as f:
        f.writelines(filtered_lines)
    
    print(f"最终清理后的文件已保存为: {output_path}")
    return output_path

if __name__ == "__main__":
    simple_clean("521-remove_monitor-read/521-remove_monitor-beautify-un.js")
