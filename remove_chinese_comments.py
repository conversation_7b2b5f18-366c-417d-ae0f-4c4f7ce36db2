#!/usr/bin/env python3
import re
import sys

def remove_chinese_comments(input_file, output_file):
    """Remove only comment lines that contain Chinese characters, preserve code"""

    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    cleaned_lines = []
    in_multiline_comment = False

    for line in lines:
        stripped = line.strip()

        # Always keep empty lines
        if not stripped:
            cleaned_lines.append(line)
            continue

        # Handle multiline comments
        if '/*' in line and '*/' in line:
            # Single line multiline comment with Chinese - remove it
            if re.search(r'[\u4e00-\u9fff]', line):
                continue
        elif '/*' in line:
            # Start of multiline comment
            if re.search(r'[\u4e00-\u9fff]', line):
                in_multiline_comment = True
                continue
        elif '*/' in line:
            # End of multiline comment
            if in_multiline_comment:
                in_multiline_comment = False
                continue

        # Skip lines inside Chinese multiline comments
        if in_multiline_comment:
            continue

        # Remove single line comments with Chinese
        if stripped.startswith('//') and re.search(r'[\u4e00-\u9fff]', line):
            continue

        # Remove comment block lines with Chinese (starting with *)
        if stripped.startswith('*') and re.search(r'[\u4e00-\u9fff]', line):
            continue

        # Remove console.log statements with Chinese
        if 'console.log' in line and re.search(r'[\u4e00-\u9fff]', line):
            continue

        # Handle inline comments - remove only the Chinese comment part
        if '//' in line:
            code_part, comment_part = line.split('//', 1)
            if re.search(r'[\u4e00-\u9fff]', comment_part):
                # Keep the code part if it exists
                if code_part.strip():
                    cleaned_lines.append(code_part.rstrip() + '\n')
                continue

        # Keep all other lines (including code with Chinese strings)
        cleaned_lines.append(line)

    # Write cleaned content
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(cleaned_lines)

    print(f"Cleaned file saved to: {output_file}")
    print(f"Original lines: {len(lines)}")
    print(f"Cleaned lines: {len(cleaned_lines)}")
    print(f"Removed lines: {len(lines) - len(cleaned_lines)}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python remove_chinese_comments.py <input_file> <output_file>")
        sys.exit(1)

    input_file = sys.argv[1]
    output_file = sys.argv[2]

    remove_chinese_comments(input_file, output_file)
