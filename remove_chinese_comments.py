#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速删除JavaScript文件中的中文注释和重复的类定义
"""

import re
import sys

def remove_duplicates_and_chinese_comments(file_path):
    """删除文件中包含中文字符的注释行和重复的类定义"""

    print(f"正在处理文件: {file_path}")

    # 读取文件
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    print(f"原始文件大小: {len(content)} 字符")

    # 删除重复的类定义，只保留最后一个
    # 找到所有 AugmentExtension 类定义的位置
    class_pattern = r'class AugmentExtension extends [^{]*\{[^}]*(?:\{[^}]*\}[^}]*)*\}'
    matches = list(re.finditer(class_pattern, content, re.DOTALL))

    if len(matches) > 1:
        print(f"找到 {len(matches)} 个重复的 AugmentExtension 类定义")
        # 删除前面的重复定义，只保留最后一个
        for match in matches[:-1]:
            start, end = match.span()
            content = content[:start] + content[end:]
            # 重新计算后续匹配的位置
            offset = end - start
            for i in range(len(matches)):
                if matches[i].start() > start:
                    matches[i] = type(matches[i])(
                        matches[i].pattern,
                        matches[i].string,
                        matches[i].start() - offset,
                        matches[i].end() - offset
                    )
        print("删除重复的类定义完成")

    # 按行分割处理中文注释
    lines = content.split('\n')
    print(f"总行数: {len(lines)}")

    # 过滤掉包含中文字符的注释行
    filtered_lines = []
    removed_count = 0

    for i, line in enumerate(lines, 1):
        # 检查是否是包含中文字符的注释行
        if re.match(r'^\s*//.*[\u4e00-\u9fff]', line):
            removed_count += 1
            continue

        # 检查是否是包含中文字符的多行注释开始
        if re.match(r'^\s*/\*.*[\u4e00-\u9fff]', line):
            removed_count += 1
            continue

        # 检查是否是包含中文字符的多行注释中间或结束
        if re.match(r'^\s*\*.*[\u4e00-\u9fff]', line):
            removed_count += 1
            continue

        # 检查是否是多行注释结束且包含中文
        if re.match(r'^\s*\*/.*[\u4e00-\u9fff]', line):
            removed_count += 1
            continue

        # 保留其他行
        filtered_lines.append(line)

    print(f"删除的中文注释行数: {removed_count}")
    print(f"剩余行数: {len(filtered_lines)}")

    # 写回文件
    output_path = file_path.replace('-clean.js', '-final.js')
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(filtered_lines))

    print(f"最终清理后的文件已保存为: {output_path}")
    return output_path

if __name__ == "__main__":
    input_file = "521-remove_monitor-read/521-remove_monitor-beautify-un-clean.js"
    remove_duplicates_and_chinese_comments(input_file)
